# services/file_service.py
import os
from django.conf import settings
from django.core.files.storage import default_storage
from django.core.files.base import ContentFile
import uuid

class FileService:
    @staticmethod
    def upload(file, folder):
        ext = os.path.splitext(file.name)[1]
        filename = f"{uuid.uuid4().hex}{ext}"
        path = os.path.join(folder, filename)
        saved_path = default_storage.save(path, ContentFile(file.read()))
        return default_storage.url(saved_path)

    @staticmethod
    def update(old_file_url, new_file, folder):
        if old_file_url:
            old_path = old_file_url.replace(settings.MEDIA_URL, "")
            if default_storage.exists(old_path):
                default_storage.delete(old_path)
        return FileService.upload(new_file, folder)

    @staticmethod
    def delete(file_url):
        if file_url:
            path = file_url.replace(settings.MEDIA_URL, "")
            if default_storage.exists(path):
                default_storage.delete(path)
