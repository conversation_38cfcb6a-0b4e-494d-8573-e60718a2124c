from django.db import models
from django.contrib.auth.models import AbstractUser
from django.utils import timezone


class User(AbstractUser):
    image_url = models.CharField(max_length=200, null=True)
    phone_number=models.CharField(max_length=20,null=False)
    date_deleted = models.DateTimeField(null=True, blank=True)
    date_updated = models.DateTimeField(auto_now=True)
    def __str__(self):
        return self.username
    
    @staticmethod
    def create_user(first_name, last_name, username, email, password, phone_number=None, image_url=None):
        user = User.objects.create_user(
            first_name=first_name,
            last_name=last_name,
            username=username,
            email=email,
            phone_number=phone_number,
            password=password
        )
        user.phone_number = phone_number
        user.image_url = image_url
        user.date_joined=timezone.now()
        return user
    

    def modify_user(self, **kwargs):
        for field, value in kwargs.items():
            if hasattr(self, field):
                setattr(self, field, value)
        self.date_updated = timezone.now()
        return self

    
    def soft_delete(self):
        self.date_deleted = timezone.now()
        self.save()
        return self
    
    def hard_delete(self):
        super().delete()