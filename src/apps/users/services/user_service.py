from django.utils import timezone
from django.contrib.auth import get_user_model
from django.core.exceptions import ObjectDoesNotExist
from users.models import User
from django.db.models import Q

User = get_user_model()
class UserService:
    @staticmethod
    def create_user(first_name,last_name,username ,email,phone_number,password,image_url =None):
        if User.objects.filter(username = username).exists():
            return ValueError("User by username {username} already exists")
        if User.objects.filter(email = email).exists():
            return ValueError("User by email {email} already exists")
        user =User.create_user(
            first_name=first_name,
            last_name=last_name,
            username=username,
            email=email,
            phone_number=phone_number,
            password=password,
            image_url=image_url
        )
        user.save()
        return user
    def modify_user(id,firstname,lastname,username,email,image_url=None):
        
        if User.objects.filter(~Q(id=id), username=username).exists():
            return ValueError("username already exists")
        if User.objects.filter(~Q(id=id), email=email).exists():
            return ValueError("email already exists")
        user =User.objects.get(id=id)
        user.modify_user(firstname,lastname,username,email,image_url)
        user.save()
        return user
    