from rest_framework import serializers
from .services.user_service import UserService
from django.contrib.auth import get_user_model

User = get_user_model()

class UserRegisterSerializer(serializers.ModelSerializer):
    password = serializers.CharField(write_only=True)
    image = serializers.ImageField(required=False, allow_null=True)
    class Meta:
        model = User
        fields = ["first_name","last_name","username", "email" ,"phone_number","password", "image"]
        
    def create(self, validated_data):
        firstname=validated_data.get("first_name")
        lastname=validated_data.get("last_name")
        username = validated_data.get("username")
        email = validated_data.get("email")
        phone_number=validated_data.get("phone_number")
        password = validated_data.get("password")
        image_file=validated_data.pop('image',None)

        user = UserService.create_user(
            first_name=firstname,
            last_name=lastname,
            username=username,
            email=email,
            phone_number=phone_number,
            password=password,
            image_url="www.emxample.com"
        )
        return user
    
class UserModifySerializer(serializers.ModelSerializer):
    class Meta :
        model=User
        fields=["first_name","last_name","username","email","image_url"]
